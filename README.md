# CumClient64 - Advanced Fabric 1.21.5 Mod with Stunning GUI

## Overview

CumClient64 is a feature-rich Fabric mod for Minecraft 1.21.5 that provides a visually stunning and highly functional click GUI system. The mod is designed with modularity, performance, and visual appeal in mind.

## Features

### 🎨 **Stunning Visual Design**
- Modern, sleek interface with smooth animations
- Rounded corners and gradient backgrounds
- Customizable color themes
- Smooth hover effects and transitions
- Professional icon-based navigation

### 🎮 **Commands**
Open the GUI using any of these commands:
- `/cc64`
- `/cumclient64` 
- `/cumclient`
- `/bbc`

### 📂 **Category System**
The GUI is organized into 12 distinct categories, each with its own icon:

1. **Slayer** (💀) - Slayer-related automation and helpers
2. **Combat** (⚔️) - PvP and combat enhancements
3. **Mining** (⛏️) - Mining automation and utilities
4. **Foraging** (🪓) - Foraging helpers and automation
5. **Fishing** (🎣) - Fishing automation and utilities
6. **Enchanting** (📖) - Enchanting helpers and automation
7. **Alchemy** (🧪) - Alchemy and brewing utilities
8. **Carpentry** (🔨) - Building and crafting helpers
9. **Taming** (🥚) - Pet and animal management
10. **Runecrafting** (🔥) - Runecrafting automation
11. **Social** (💎) - Social features and chat utilities
12. **Hunting** (🕸️) - Hunting and mob-related features

### 🔧 **Component Types**
Each category can contain modules with various component types:

- **Toggle Components** - On/off switches with smooth animations
- **Slider Components** - Numeric value controls with customizable ranges
- **Input Components** - Text input fields with validation
- **Expandable Modules** - Collapsible sections for organized settings

### ⚡ **Technical Features**
- **Smooth Animations** - All UI elements feature smooth, eased animations
- **Responsive Design** - Adapts to different screen sizes
- **Scrollable Content** - Handle large amounts of settings with smooth scrolling
- **Keyboard Support** - Full keyboard navigation and input support
- **Memory Efficient** - Optimized rendering and minimal resource usage

## Architecture

### 🏗️ **Modular Design**
The codebase is structured for maximum reusability and maintainability:

```
src/client/java/leaf/untitled/client/
├── command/           # Command registration and handling
├── gui/
│   ├── animation/     # Animation system
│   ├── category/      # Category management
│   ├── component/     # Reusable UI components
│   ├── module/        # Module system
│   ├── render/        # Custom rendering utilities
│   └── theme/         # Theming and styling
└── ClickGuiScreen.java # Main GUI screen
```

### 🎨 **Theme System**
- Centralized color management
- Easy theme customization
- Consistent styling across all components
- Support for color interpolation and animations

### 🔄 **Animation System**
- Smooth easing functions
- Configurable animation speeds
- Efficient animation updates
- Support for multiple simultaneous animations

## Usage

### Opening the GUI
Use any of the registered commands to open the GUI:
```
/cc64
```

### Navigation
- Click on category icons on the left to switch between categories
- Click on module headers to expand/collapse them
- Use mouse wheel to scroll through long lists of modules
- Press ESC to close the GUI

### Interacting with Components
- **Toggles**: Click to enable/disable
- **Sliders**: Click and drag to adjust values
- **Input Fields**: Click to focus, type to enter text

## Example Modules

### Combat Category
- **Auto Clicker**: Automated clicking with configurable CPS
- **Reach**: Extend attack reach distance

### Mining Category  
- **Nuker**: Automated block breaking with range control
- **X-Ray**: Highlight specific ore types

### Slayer Category
- **Slayer Helper**: Automated slayer quest management

## Development

### Adding New Categories
1. Add the category to `CategoryType.java`
2. Create the corresponding icon texture
3. Add modules in the `initializeCategories()` method

### Adding New Components
1. Extend the `GuiComponent` base class
2. Implement rendering and interaction methods
3. Add to modules as needed

### Customizing Themes
Modify `GuiTheme.java` to change colors, dimensions, and animation settings.

## Building

```bash
./gradlew build
```

## Installation

1. Install Fabric Loader for Minecraft 1.21.5
2. Place the compiled JAR in your mods folder
3. Launch Minecraft and enjoy!

## Requirements

- Minecraft 1.21.5
- Fabric Loader 0.16.14+
- Fabric API 0.126.0+

## License

All rights reserved. See LICENSE.txt for details.
