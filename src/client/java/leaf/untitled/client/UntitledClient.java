package leaf.untitled.client;

import leaf.untitled.client.command.GuiCommand;
import leaf.untitled.client.gui.ClickGuiScreen;
import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandRegistrationCallback;

public class UntitledClient implements ClientModInitializer {

    public static ClickGuiScreen clickGui;

    @Override
    public void onInitializeClient() {
        clickGui = new ClickGuiScreen();

        // Register commands
        ClientCommandRegistrationCallback.EVENT.register((dispatcher, registryAccess) -> {
            GuiCommand.register(dispatcher);
        });
    }
}
