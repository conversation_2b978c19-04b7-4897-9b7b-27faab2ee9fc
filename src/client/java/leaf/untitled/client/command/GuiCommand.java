package leaf.untitled.client.command;

import com.mojang.brigadier.CommandDispatcher;
import leaf.untitled.client.UntitledClient;
import net.fabricmc.fabric.api.client.command.v2.FabricClientCommandSource;
import net.minecraft.client.MinecraftClient;

import static net.fabricmc.fabric.api.client.command.v2.ClientCommandManager.literal;

public class GuiCommand {
    
    public static void register(CommandDispatcher<FabricClientCommandSource> dispatcher) {
        dispatcher.register(literal("cc64")
            .executes(context -> {
                openGui();
                return 1;
            }));
            
        dispatcher.register(literal("cumclient64")
            .executes(context -> {
                openGui();
                return 1;
            }));
            
        dispatcher.register(literal("cumclient")
            .executes(context -> {
                openGui();
                return 1;
            }));
            
        dispatcher.register(literal("bbc")
            .executes(context -> {
                openGui();
                return 1;
            }));
    }
    
    private static void openGui() {
        MinecraftClient.getInstance().execute(() -> {
            MinecraftClient.getInstance().setScreen(UntitledClient.clickGui);
        });
    }
}
