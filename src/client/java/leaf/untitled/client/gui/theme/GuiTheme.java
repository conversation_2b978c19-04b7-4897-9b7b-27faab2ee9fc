package leaf.untitled.client.gui.theme;

import java.awt.Color;

public class GuiTheme {
    
    // Primary colors
    public static final int BACKGROUND = new Color(20, 20, 25, 240).getRGB();
    public static final int PANEL_BACKGROUND = new Color(30, 30, 35, 200).getRGB();
    public static final int CATEGORY_BACKGROUND = new Color(40, 40, 45, 180).getRGB();
    
    // Accent colors
    public static final int ACCENT_PRIMARY = new Color(120, 80, 255, 255).getRGB();
    public static final int ACCENT_SECONDARY = new Color(80, 120, 255, 255).getRGB();
    public static final int ACCENT_HOVER = new Color(140, 100, 255, 255).getRGB();
    
    // Text colors
    public static final int TEXT_PRIMARY = new Color(255, 255, 255, 255).getRGB();
    public static final int TEXT_SECONDARY = new Color(180, 180, 180, 255).getRGB();
    public static final int TEXT_DISABLED = new Color(120, 120, 120, 255).getRGB();
    
    // Component colors
    public static final int TOGGLE_ENABLED = new Color(80, 200, 120, 255).getRGB();
    public static final int TOGGLE_DISABLED = new Color(60, 60, 65, 255).getRGB();
    public static final int SLIDER_TRACK = new Color(50, 50, 55, 255).getRGB();
    public static final int SLIDER_THUMB = new Color(120, 80, 255, 255).getRGB();
    
    // Border colors
    public static final int BORDER_LIGHT = new Color(80, 80, 85, 255).getRGB();
    public static final int BORDER_DARK = new Color(20, 20, 25, 255).getRGB();
    
    // Animation settings
    public static final float ANIMATION_SPEED = 0.15f;
    public static final float HOVER_ANIMATION_SPEED = 0.25f;
    
    // Dimensions
    public static final int CATEGORY_WIDTH = 60;
    public static final int PANEL_WIDTH = 300;
    public static final int COMPONENT_HEIGHT = 25;
    public static final int PADDING = 8;
    public static final int BORDER_RADIUS = 6;
    
    // Utility methods for color manipulation
    public static int withAlpha(int color, int alpha) {
        return (color & 0x00FFFFFF) | (alpha << 24);
    }
    
    public static int interpolateColor(int color1, int color2, float progress) {
        int r1 = (color1 >> 16) & 0xFF;
        int g1 = (color1 >> 8) & 0xFF;
        int b1 = color1 & 0xFF;
        int a1 = (color1 >> 24) & 0xFF;
        
        int r2 = (color2 >> 16) & 0xFF;
        int g2 = (color2 >> 8) & 0xFF;
        int b2 = color2 & 0xFF;
        int a2 = (color2 >> 24) & 0xFF;
        
        int r = (int) (r1 + (r2 - r1) * progress);
        int g = (int) (g1 + (g2 - g1) * progress);
        int b = (int) (b1 + (b2 - b1) * progress);
        int a = (int) (a1 + (a2 - a1) * progress);
        
        return (a << 24) | (r << 16) | (g << 8) | b;
    }
}
