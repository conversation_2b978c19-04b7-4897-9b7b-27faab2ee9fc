package leaf.untitled.client.gui.category;

import leaf.untitled.client.gui.animation.GuiAnimation;
import leaf.untitled.client.gui.module.GuiModule;
import leaf.untitled.client.gui.render.GuiRenderer;
import leaf.untitled.client.gui.theme.GuiTheme;
import net.minecraft.client.gui.DrawContext;

import java.util.ArrayList;
import java.util.List;

public class GuiCategory {
    
    private CategoryType type;
    private List<GuiModule> modules;
    private boolean selected;
    private GuiAnimation selectionAnimation;
    private GuiAnimation hoverAnimation;
    private int x, y, width, height;
    private int scrollOffset;
    
    public GuiCategory(CategoryType type) {
        this.type = type;
        this.modules = new ArrayList<>();
        this.selected = false;
        this.selectionAnimation = new GuiAnimation(GuiTheme.ANIMATION_SPEED);
        this.hoverAnimation = new GuiAnimation(GuiTheme.HOVER_ANIMATION_SPEED);
        this.scrollOffset = 0;
    }
    
    public void renderIcon(DrawContext context, int x, int y, int size, int mouseX, int mouseY) {
        this.x = x;
        this.y = y;
        this.width = size;
        this.height = size;
        
        // Update animations
        selectionAnimation.update();
        hoverAnimation.update();
        
        boolean isHovered = isIconHovered(mouseX, mouseY);
        hoverAnimation.setTarget(isHovered ? 1.0f : 0.0f);
        
        // Background
        int bgColor = GuiTheme.interpolateColor(
            GuiTheme.CATEGORY_BACKGROUND,
            GuiTheme.ACCENT_PRIMARY,
            Math.max(selectionAnimation.getValue(), hoverAnimation.getValue() * 0.5f)
        );
        GuiRenderer.drawRoundedRect(context, x, y, size, size, GuiTheme.BORDER_RADIUS, bgColor);
        
        // Icon
        int iconSize = size - 16;
        int iconX = x + (size - iconSize) / 2;
        int iconY = y + (size - iconSize) / 2;
        
        int iconColor = GuiTheme.interpolateColor(
            GuiTheme.TEXT_SECONDARY,
            GuiTheme.TEXT_PRIMARY,
            Math.max(selectionAnimation.getValue(), hoverAnimation.getValue())
        );
        
        // For now, draw a simple colored rectangle as placeholder for icon
        // In a real implementation, you would use GuiRenderer.drawIcon() with the actual texture
        GuiRenderer.drawRoundedRect(context, iconX, iconY, iconSize, iconSize, 4, iconColor);
        
        // Selection indicator
        if (selectionAnimation.getValue() > 0.01f) {
            int indicatorWidth = 3;
            int indicatorHeight = (int) (size * 0.6f * selectionAnimation.getValue());
            int indicatorX = x - indicatorWidth - 2;
            int indicatorY = y + (size - indicatorHeight) / 2;
            
            GuiRenderer.drawRoundedRect(context, indicatorX, indicatorY, indicatorWidth, indicatorHeight, 
                indicatorWidth / 2, GuiTheme.ACCENT_PRIMARY);
        }
    }
    
    public void renderPanel(DrawContext context, int panelX, int panelY, int panelWidth, int panelHeight, int mouseX, int mouseY, float delta) {
        if (!selected) return;
        
        // Panel background
        GuiRenderer.drawRoundedRect(context, panelX, panelY, panelWidth, panelHeight, GuiTheme.BORDER_RADIUS, GuiTheme.PANEL_BACKGROUND);
        
        // Category title
        String title = type.getDisplayName();
        int titleY = panelY + GuiTheme.PADDING;
        GuiRenderer.drawText(context, title, panelX + GuiTheme.PADDING, titleY, GuiTheme.TEXT_PRIMARY);
        
        // Separator line
        int separatorY = titleY + 12;
        context.fill(panelX + GuiTheme.PADDING, separatorY, panelX + panelWidth - GuiTheme.PADDING, separatorY + 1, GuiTheme.BORDER_LIGHT);
        
        // Modules
        int moduleY = separatorY + GuiTheme.PADDING + scrollOffset;
        int contentHeight = panelHeight - (separatorY - panelY) - GuiTheme.PADDING * 2;
        
        // Enable scissor for scrolling
        context.enableScissor(panelX, separatorY + GuiTheme.PADDING, panelX + panelWidth, panelY + panelHeight - GuiTheme.PADDING);
        
        for (GuiModule module : modules) {
            if (moduleY + module.getHeight() > separatorY + GuiTheme.PADDING && moduleY < panelY + panelHeight - GuiTheme.PADDING) {
                module.setPosition(panelX + GuiTheme.PADDING, moduleY);
                module.setWidth(panelWidth - GuiTheme.PADDING * 2);
                module.render(context, mouseX, mouseY, delta);
            }
            moduleY += module.getHeight() + 4;
        }
        
        context.disableScissor();
        
        // Scrollbar (if needed)
        int totalContentHeight = getTotalContentHeight();
        if (totalContentHeight > contentHeight) {
            renderScrollbar(context, panelX + panelWidth - 8, separatorY + GuiTheme.PADDING, 6, contentHeight, totalContentHeight);
        }
    }
    
    private void renderScrollbar(DrawContext context, int x, int y, int width, int height, int totalHeight) {
        // Scrollbar track
        GuiRenderer.drawRoundedRect(context, x, y, width, height, width / 2, GuiTheme.SLIDER_TRACK);
        
        // Scrollbar thumb
        float scrollRatio = (float) height / totalHeight;
        int thumbHeight = Math.max(20, (int) (height * scrollRatio));
        float scrollProgress = (float) -scrollOffset / (totalHeight - height);
        int thumbY = y + (int) ((height - thumbHeight) * scrollProgress);
        
        GuiRenderer.drawRoundedRect(context, x, thumbY, width, thumbHeight, width / 2, GuiTheme.SLIDER_THUMB);
    }
    
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (!selected) return false;
        
        for (GuiModule module : modules) {
            if (module.mouseClicked(mouseX, mouseY, button)) {
                return true;
            }
        }
        
        return false;
    }
    
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (!selected) return false;
        
        for (GuiModule module : modules) {
            if (module.mouseReleased(mouseX, mouseY, button)) {
                return true;
            }
        }
        
        return false;
    }
    
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (!selected) return false;
        
        for (GuiModule module : modules) {
            if (module.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
                return true;
            }
        }
        
        return false;
    }
    
    public boolean mouseScrolled(double mouseX, double mouseY, double horizontalAmount, double verticalAmount) {
        if (!selected) return false;
        
        int maxScroll = Math.max(0, getTotalContentHeight() - 200); // Approximate content height
        scrollOffset = Math.max(-maxScroll, Math.min(0, scrollOffset + (int) (verticalAmount * 20)));
        
        return true;
    }
    
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (!selected) return false;
        
        for (GuiModule module : modules) {
            if (module.keyPressed(keyCode, scanCode, modifiers)) {
                return true;
            }
        }
        
        return false;
    }
    
    public boolean charTyped(char chr, int modifiers) {
        if (!selected) return false;
        
        for (GuiModule module : modules) {
            if (module.charTyped(chr, modifiers)) {
                return true;
            }
        }
        
        return false;
    }
    
    public void update() {
        for (GuiModule module : modules) {
            module.update();
        }
    }
    
    public boolean isIconHovered(double mouseX, double mouseY) {
        return mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;
    }
    
    public void setSelected(boolean selected) {
        this.selected = selected;
        selectionAnimation.setTarget(selected ? 1.0f : 0.0f);
        if (selected) {
            scrollOffset = 0; // Reset scroll when selecting category
        }
    }
    
    public void addModule(GuiModule module) {
        modules.add(module);
    }
    
    public void removeModule(GuiModule module) {
        modules.remove(module);
    }
    
    private int getTotalContentHeight() {
        int height = 0;
        for (GuiModule module : modules) {
            height += module.getHeight() + 4;
        }
        return height;
    }
    
    public CategoryType getType() {
        return type;
    }
    
    public boolean isSelected() {
        return selected;
    }
    
    public List<GuiModule> getModules() {
        return modules;
    }
}
