package leaf.untitled.client.gui.category;

import net.minecraft.util.Identifier;

public enum CategoryType {
    SLAYER("Slayer", "textures/gui/icons/skull.png"),
    COMBAT("Combat", "textures/gui/icons/sword.png"),
    MINING("Mining", "textures/gui/icons/pickaxe.png"),
    FORAGING("Foraging", "textures/gui/icons/axe.png"),
    FISHING("Fishing", "textures/gui/icons/fishing_rod.png"),
    ENCHANTING("Enchanting", "textures/gui/icons/book.png"),
    ALCHEMY("Alchemy", "textures/gui/icons/bottle.png"),
    CARPENTRY("Carpentry", "textures/gui/icons/crafting_table.png"),
    TAMING("Taming", "textures/gui/icons/spawn_egg.png"),
    RUNECRAFTING("Runecrafting", "textures/gui/icons/magma_cream.png"),
    SOCIAL("Social", "textures/gui/icons/emerald.png"),
    HUNTING("Hunting", "textures/gui/icons/cobweb.png");
    
    private final String displayName;
    private final Identifier icon;
    
    CategoryType(String displayName, String iconPath) {
        this.displayName = displayName;
        this.icon = Identifier.of("untitled", iconPath);
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public Identifier getIcon() {
        return icon;
    }
}
