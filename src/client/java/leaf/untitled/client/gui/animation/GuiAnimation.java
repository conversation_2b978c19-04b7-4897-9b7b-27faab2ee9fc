package leaf.untitled.client.gui.animation;

public class GuiAnimation {
    
    private float current;
    private float target;
    private float speed;
    private boolean finished;
    
    public GuiAnimation(float speed) {
        this.speed = speed;
        this.current = 0;
        this.target = 0;
        this.finished = true;
    }
    
    public void update() {
        if (Math.abs(current - target) < 0.001f) {
            current = target;
            finished = true;
            return;
        }
        
        finished = false;
        current += (target - current) * speed;
    }
    
    public void setTarget(float target) {
        this.target = target;
        this.finished = false;
    }
    
    public float getValue() {
        return current;
    }
    
    public float getTarget() {
        return target;
    }
    
    public boolean isFinished() {
        return finished;
    }
    
    public void setSpeed(float speed) {
        this.speed = speed;
    }
    
    public void setValue(float value) {
        this.current = value;
        this.target = value;
        this.finished = true;
    }
    
    // Easing functions
    public static float easeInOut(float t) {
        return t * t * (3.0f - 2.0f * t);
    }
    
    public static float easeOut(float t) {
        return 1.0f - (1.0f - t) * (1.0f - t);
    }
    
    public static float easeIn(float t) {
        return t * t;
    }
}
