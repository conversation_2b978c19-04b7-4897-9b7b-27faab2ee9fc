package leaf.untitled.client.gui.module;

import leaf.untitled.client.gui.animation.GuiAnimation;
import leaf.untitled.client.gui.component.GuiComponent;
import leaf.untitled.client.gui.render.GuiRenderer;
import leaf.untitled.client.gui.theme.GuiTheme;
import net.minecraft.client.gui.DrawContext;

import java.util.ArrayList;
import java.util.List;

public class GuiModule {
    
    private String name;
    private List<GuiComponent> components;
    private boolean expanded;
    private GuiAnimation expandAnimation;
    private GuiAnimation hoverAnimation;
    private int x, y, width;
    private int headerHeight = 25;
    
    public GuiModule(String name) {
        this.name = name;
        this.components = new ArrayList<>();
        this.expanded = false;
        this.expandAnimation = new GuiAnimation(GuiTheme.ANIMATION_SPEED);
        this.hoverAnimation = new GuiAnimation(GuiTheme.HOVER_ANIMATION_SPEED);
    }
    
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Update animations
        expandAnimation.update();
        hoverAnimation.update();
        
        boolean isHeaderHovered = isHeaderHovered(mouseX, mouseY);
        hoverAnimation.setTarget(isHeaderHovered ? 1.0f : 0.0f);
        
        // Header background
        int headerColor = GuiTheme.interpolateColor(
            GuiTheme.CATEGORY_BACKGROUND,
            GuiTheme.ACCENT_PRIMARY,
            hoverAnimation.getValue() * 0.3f
        );
        GuiRenderer.drawRoundedRect(context, x, y, width, headerHeight, GuiTheme.BORDER_RADIUS, headerColor);
        
        // Header text
        GuiRenderer.drawText(context, name, x + GuiTheme.PADDING, y + (headerHeight - 8) / 2, GuiTheme.TEXT_PRIMARY);
        
        // Expand arrow
        String arrow = expanded ? "▼" : "▶";
        int arrowX = x + width - GuiTheme.PADDING - context.getTextRenderer().getWidth(arrow);
        GuiRenderer.drawText(context, arrow, arrowX, y + (headerHeight - 8) / 2, GuiTheme.TEXT_SECONDARY);
        
        // Components (if expanded)
        if (expandAnimation.getValue() > 0.01f) {
            int componentY = y + headerHeight;
            int maxHeight = getExpandedHeight() - headerHeight;
            int currentHeight = (int) (maxHeight * expandAnimation.getValue());
            
            // Clip rendering to animated height
            context.enableScissor(x, componentY, x + width, componentY + currentHeight);
            
            for (GuiComponent component : components) {
                if (component.isVisible()) {
                    component.render(context, mouseX, mouseY, delta);
                    componentY += component.getHeight() + 2;
                }
            }
            
            context.disableScissor();
        }
    }
    
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (isHeaderHovered(mouseX, mouseY) && button == 0) {
            toggleExpanded();
            return true;
        }
        
        if (expanded) {
            for (GuiComponent component : components) {
                if (component.mouseClicked(mouseX, mouseY, button)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (expanded) {
            for (GuiComponent component : components) {
                if (component.mouseReleased(mouseX, mouseY, button)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (expanded) {
            for (GuiComponent component : components) {
                if (component.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (expanded) {
            for (GuiComponent component : components) {
                if (component.keyPressed(keyCode, scanCode, modifiers)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    public boolean charTyped(char chr, int modifiers) {
        if (expanded) {
            for (GuiComponent component : components) {
                if (component.charTyped(chr, modifiers)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    public void update() {
        for (GuiComponent component : components) {
            component.update();
        }
    }
    
    private boolean isHeaderHovered(double mouseX, double mouseY) {
        return mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + headerHeight;
    }
    
    public void toggleExpanded() {
        setExpanded(!expanded);
    }
    
    public void setExpanded(boolean expanded) {
        this.expanded = expanded;
        expandAnimation.setTarget(expanded ? 1.0f : 0.0f);
    }
    
    public void addComponent(GuiComponent component) {
        components.add(component);
        updateComponentPositions();
    }
    
    public void removeComponent(GuiComponent component) {
        components.remove(component);
        updateComponentPositions();
    }
    
    private void updateComponentPositions() {
        int componentY = y + headerHeight + 2;
        for (GuiComponent component : components) {
            component.setPosition(x + 4, componentY);
            component.setSize(width - 8, component.getHeight());
            componentY += component.getHeight() + 2;
        }
    }
    
    public void setPosition(int x, int y) {
        this.x = x;
        this.y = y;
        updateComponentPositions();
    }
    
    public void setWidth(int width) {
        this.width = width;
        updateComponentPositions();
    }
    
    public int getHeight() {
        if (expanded) {
            return (int) (headerHeight + (getExpandedHeight() - headerHeight) * expandAnimation.getValue());
        }
        return headerHeight;
    }
    
    private int getExpandedHeight() {
        int height = headerHeight + 2;
        for (GuiComponent component : components) {
            if (component.isVisible()) {
                height += component.getHeight() + 2;
            }
        }
        return height;
    }
    
    public String getName() {
        return name;
    }
    
    public boolean isExpanded() {
        return expanded;
    }
    
    public List<GuiComponent> getComponents() {
        return components;
    }
}
