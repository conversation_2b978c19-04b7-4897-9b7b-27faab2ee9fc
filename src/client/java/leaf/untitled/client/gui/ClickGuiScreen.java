package leaf.untitled.client.gui;

import leaf.untitled.client.gui.animation.GuiAnimation;
import leaf.untitled.client.gui.category.CategoryType;
import leaf.untitled.client.gui.category.GuiCategory;
import leaf.untitled.client.gui.component.InputComponent;
import leaf.untitled.client.gui.component.SliderComponent;
import leaf.untitled.client.gui.component.ToggleComponent;
import leaf.untitled.client.gui.module.GuiModule;
import leaf.untitled.client.gui.render.GuiRenderer;
import leaf.untitled.client.gui.theme.GuiTheme;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import org.lwjgl.glfw.GLFW;

import java.util.ArrayList;
import java.util.List;

public class ClickGuiScreen extends Screen {
    
    private List<GuiCategory> categories;
    private GuiCategory selectedCategory;
    private GuiAnimation openAnimation;
    private boolean closing;
    
    public ClickGuiScreen() {
        super(Text.literal("CumClient64"));
        this.categories = new ArrayList<>();
        this.openAnimation = new GuiAnimation(GuiTheme.ANIMATION_SPEED);
        this.closing = false;
        
        initializeCategories();
    }
    
    private void initializeCategories() {
        // Create all categories
        for (CategoryType type : CategoryType.values()) {
            GuiCategory category = new GuiCategory(type);
            categories.add(category);
            
            // Add example modules for demonstration
            addExampleModules(category);
        }
        
        // Select first category by default
        if (!categories.isEmpty()) {
            selectedCategory = categories.get(0);
            selectedCategory.setSelected(true);
        }
    }
    
    private void addExampleModules(GuiCategory category) {
        // Add example modules based on category type
        switch (category.getType()) {
            case COMBAT:
                addCombatModules(category);
                break;
            case MINING:
                addMiningModules(category);
                break;
            case SLAYER:
                addSlayerModules(category);
                break;
            // Add more cases as needed
            default:
                addGenericModules(category);
                break;
        }
    }
    
    private void addCombatModules(GuiCategory category) {
        GuiModule autoClicker = new GuiModule("Auto Clicker");
        autoClicker.addComponent(new ToggleComponent("Enabled", 0, 0, 200, false));
        autoClicker.addComponent(new SliderComponent("CPS", 0, 0, 200, 1.0, 20.0, 10.0));
        autoClicker.addComponent(new ToggleComponent("Only When Holding", 0, 0, 200, true));
        category.addModule(autoClicker);
        
        GuiModule reach = new GuiModule("Reach");
        reach.addComponent(new ToggleComponent("Enabled", 0, 0, 200, false));
        reach.addComponent(new SliderComponent("Distance", 0, 0, 200, 3.0, 6.0, 3.5));
        category.addModule(reach);
    }
    
    private void addMiningModules(GuiCategory category) {
        GuiModule nuker = new GuiModule("Nuker");
        nuker.addComponent(new ToggleComponent("Enabled", 0, 0, 200, false));
        nuker.addComponent(new SliderComponent("Range", 0, 0, 200, 1.0, 10.0, 5.0));
        nuker.addComponent(new ToggleComponent("Only Ores", 0, 0, 200, true));
        category.addModule(nuker);
        
        GuiModule xray = new GuiModule("X-Ray");
        xray.addComponent(new ToggleComponent("Enabled", 0, 0, 200, false));
        xray.addComponent(new InputComponent("Blocks", 0, 0, 200, "diamond_ore,iron_ore"));
        category.addModule(xray);
    }
    
    private void addSlayerModules(GuiCategory category) {
        GuiModule slayerHelper = new GuiModule("Slayer Helper");
        slayerHelper.addComponent(new ToggleComponent("Auto Accept", 0, 0, 200, false));
        slayerHelper.addComponent(new ToggleComponent("Auto Start", 0, 0, 200, false));
        slayerHelper.addComponent(new SliderComponent("Tier", 0, 0, 200, 1.0, 5.0, 3.0));
        category.addModule(slayerHelper);
    }
    
    private void addGenericModules(GuiCategory category) {
        GuiModule exampleModule = new GuiModule("Example Module");
        exampleModule.addComponent(new ToggleComponent("Toggle Option", 0, 0, 200, false));
        exampleModule.addComponent(new SliderComponent("Slider Option", 0, 0, 200, 0.0, 100.0, 50.0));
        exampleModule.addComponent(new InputComponent("Text Option", 0, 0, 200, "Default Value"));
        category.addModule(exampleModule);
    }
    
    @Override
    protected void init() {
        super.init();
        openAnimation.setTarget(1.0f);
        closing = false;
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Update animations
        openAnimation.update();
        
        // Background overlay
        int overlayAlpha = (int) (100 * openAnimation.getValue());
        context.fill(0, 0, width, height, (overlayAlpha << 24));
        
        // Calculate GUI dimensions
        int guiWidth = GuiTheme.CATEGORY_WIDTH + GuiTheme.PANEL_WIDTH;
        int guiHeight = Math.min(height - 40, 500);
        int guiX = (width - guiWidth) / 2;
        int guiY = (height - guiHeight) / 2;
        
        // Apply scale animation
        float scale = 0.8f + 0.2f * GuiAnimation.easeOut(openAnimation.getValue());
        
        context.getMatrices().push();
        context.getMatrices().translate(width / 2f, height / 2f, 0);
        context.getMatrices().scale(scale, scale, 1.0f);
        context.getMatrices().translate(-width / 2f, -height / 2f, 0);
        
        // Main GUI background
        GuiRenderer.drawRoundedRect(context, guiX, guiY, guiWidth, guiHeight, GuiTheme.BORDER_RADIUS, GuiTheme.BACKGROUND);
        
        // Category sidebar
        renderCategorySidebar(context, guiX, guiY, GuiTheme.CATEGORY_WIDTH, guiHeight, mouseX, mouseY);
        
        // Main panel
        if (selectedCategory != null) {
            int panelX = guiX + GuiTheme.CATEGORY_WIDTH;
            selectedCategory.renderPanel(context, panelX, guiY, GuiTheme.PANEL_WIDTH, guiHeight, mouseX, mouseY, delta);
        }
        
        context.getMatrices().pop();
        
        super.render(context, mouseX, mouseY, delta);
    }
    
    private void renderCategorySidebar(DrawContext context, int x, int y, int width, int height, int mouseX, int mouseY) {
        // Sidebar background
        GuiRenderer.drawRoundedRect(context, x, y, width, height, GuiTheme.BORDER_RADIUS, GuiTheme.CATEGORY_BACKGROUND);
        
        // Categories
        int categoryY = y + GuiTheme.PADDING;
        int categorySize = width - GuiTheme.PADDING * 2;
        
        for (GuiCategory category : categories) {
            if (categoryY + categorySize <= y + height - GuiTheme.PADDING) {
                category.renderIcon(context, x + GuiTheme.PADDING, categoryY, categorySize, mouseX, mouseY);
                categoryY += categorySize + 4;
            }
        }
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (openAnimation.getValue() < 0.9f) return false;
        
        // Check category selection
        for (GuiCategory category : categories) {
            if (category.isIconHovered(mouseX, mouseY) && button == 0) {
                selectCategory(category);
                return true;
            }
        }
        
        // Forward to selected category
        if (selectedCategory != null) {
            return selectedCategory.mouseClicked(mouseX, mouseY, button);
        }
        
        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (selectedCategory != null) {
            return selectedCategory.mouseReleased(mouseX, mouseY, button);
        }
        return super.mouseReleased(mouseX, mouseY, button);
    }
    
    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (selectedCategory != null) {
            return selectedCategory.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
        }
        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }
    
    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double horizontalAmount, double verticalAmount) {
        if (selectedCategory != null) {
            return selectedCategory.mouseScrolled(mouseX, mouseY, horizontalAmount, verticalAmount);
        }
        return super.mouseScrolled(mouseX, mouseY, horizontalAmount, verticalAmount);
    }
    
    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (keyCode == GLFW.GLFW_KEY_ESCAPE) {
            close();
            return true;
        }
        
        if (selectedCategory != null) {
            return selectedCategory.keyPressed(keyCode, scanCode, modifiers);
        }
        
        return super.keyPressed(keyCode, scanCode, modifiers);
    }
    
    @Override
    public boolean charTyped(char chr, int modifiers) {
        if (selectedCategory != null) {
            return selectedCategory.charTyped(chr, modifiers);
        }
        return super.charTyped(chr, modifiers);
    }
    
    @Override
    public void tick() {
        super.tick();
        
        // Update categories
        for (GuiCategory category : categories) {
            category.update();
        }
        
        // Handle closing animation
        if (closing && openAnimation.getValue() <= 0.01f) {
            if (client != null) {
                client.setScreen(null);
            }
        }
    }
    
    @Override
    public void close() {
        closing = true;
        openAnimation.setTarget(0.0f);
    }
    
    @Override
    public boolean shouldPause() {
        return false;
    }
    
    private void selectCategory(GuiCategory category) {
        if (selectedCategory != null) {
            selectedCategory.setSelected(false);
        }
        
        selectedCategory = category;
        category.setSelected(true);
    }
    
    public List<GuiCategory> getCategories() {
        return categories;
    }
    
    public GuiCategory getSelectedCategory() {
        return selectedCategory;
    }
}
