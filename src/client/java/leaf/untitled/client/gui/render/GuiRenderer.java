package leaf.untitled.client.gui.render;

import leaf.untitled.client.gui.theme.GuiTheme;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.render.RenderLayer;
import net.minecraft.util.Identifier;

public class GuiRenderer {
    
    public static void drawRoundedRect(DrawContext context, int x, int y, int width, int height, int radius, int color) {
        // Draw main rectangle
        context.fill(x + radius, y, x + width - radius, y + height, color);
        context.fill(x, y + radius, x + width, y + height - radius, color);
        
        // Draw corners
        drawRoundedCorner(context, x, y, radius, color, 0); // Top-left
        drawRoundedCorner(context, x + width - radius, y, radius, color, 1); // Top-right
        drawRoundedCorner(context, x, y + height - radius, radius, color, 2); // Bottom-left
        drawRoundedCorner(context, x + width - radius, y + height - radius, radius, color, 3); // Bottom-right
    }
    
    private static void drawRoundedCorner(DrawContext context, int x, int y, int radius, int color, int corner) {
        for (int i = 0; i < radius; i++) {
            for (int j = 0; j < radius; j++) {
                double distance = Math.sqrt((i - radius + 0.5) * (i - radius + 0.5) + (j - radius + 0.5) * (j - radius + 0.5));
                if (distance <= radius) {
                    int pixelX = x, pixelY = y;
                    
                    switch (corner) {
                        case 0: // Top-left
                            pixelX = x + radius - 1 - i;
                            pixelY = y + radius - 1 - j;
                            break;
                        case 1: // Top-right
                            pixelX = x + i;
                            pixelY = y + radius - 1 - j;
                            break;
                        case 2: // Bottom-left
                            pixelX = x + radius - 1 - i;
                            pixelY = y + j;
                            break;
                        case 3: // Bottom-right
                            pixelX = x + i;
                            pixelY = y + j;
                            break;
                    }
                    
                    context.fill(pixelX, pixelY, pixelX + 1, pixelY + 1, color);
                }
            }
        }
    }
    
    public static void drawGradientRect(DrawContext context, int x, int y, int width, int height, int colorTop, int colorBottom) {
        context.fillGradient(x, y, x + width, y + height, colorTop, colorBottom);
    }
    
    public static void drawBorder(DrawContext context, int x, int y, int width, int height, int thickness, int color) {
        // Top
        context.fill(x, y, x + width, y + thickness, color);
        // Bottom
        context.fill(x, y + height - thickness, x + width, y + height, color);
        // Left
        context.fill(x, y, x + thickness, y + height, color);
        // Right
        context.fill(x + width - thickness, y, x + width, y + height, color);
    }
    
    public static void drawIcon(DrawContext context, Identifier texture, int x, int y, int size, int color) {
        // For now, draw a simple colored rectangle as placeholder
        // In a real implementation, you would use the actual texture rendering
        context.fill(x, y, x + size, y + size, color);
    }

    public static void drawCenteredText(DrawContext context, String text, int x, int y, int color) {
        net.minecraft.client.font.TextRenderer textRenderer = net.minecraft.client.MinecraftClient.getInstance().textRenderer;
        int textWidth = textRenderer.getWidth(text);
        context.drawText(textRenderer, text, x - textWidth / 2, y, color, false);
    }

    public static void drawText(DrawContext context, String text, int x, int y, int color) {
        net.minecraft.client.font.TextRenderer textRenderer = net.minecraft.client.MinecraftClient.getInstance().textRenderer;
        context.drawText(textRenderer, text, x, y, color, false);
    }
    
    public static boolean isMouseOver(int mouseX, int mouseY, int x, int y, int width, int height) {
        return mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;
    }
}
