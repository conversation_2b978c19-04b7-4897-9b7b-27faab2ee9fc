package leaf.untitled.client.gui.component;

import leaf.untitled.client.gui.render.GuiRenderer;
import leaf.untitled.client.gui.theme.GuiTheme;
import net.minecraft.client.gui.DrawContext;
import org.lwjgl.glfw.GLFW;

import java.util.function.Consumer;

public class InputComponent extends GuiComponent {
    
    private String value;
    private String placeholder;
    private boolean focused;
    private int cursorPosition;
    private int selectionStart;
    private int selectionEnd;
    private long lastCursorBlink;
    private Consumer<String> onValueChange;
    private boolean numbersOnly;
    
    public InputComponent(String name, int x, int y, int width, String defaultValue) {
        super(name, x, y, width, GuiTheme.COMPONENT_HEIGHT);
        this.value = defaultValue != null ? defaultValue : "";
        this.placeholder = "";
        this.cursorPosition = this.value.length();
        this.selectionStart = 0;
        this.selectionEnd = 0;
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        if (!visible) return;
        
        boolean isHovered = isMouseOver(mouseX, mouseY);
        hoverAnimation.setTarget(isHovered || focused ? 1.0f : 0.0f);
        
        // Background
        int bgColor = GuiTheme.interpolateColor(
            GuiTheme.PANEL_BACKGROUND,
            GuiTheme.CATEGORY_BACKGROUND,
            hoverAnimation.getValue()
        );
        GuiRenderer.drawRoundedRect(context, x, y, width, height, GuiTheme.BORDER_RADIUS, bgColor);
        
        // Border for focused state
        if (focused) {
            GuiRenderer.drawBorder(context, x, y, width, height, 1, GuiTheme.ACCENT_PRIMARY);
        }
        
        // Label
        int textColor = enabled ? GuiTheme.TEXT_PRIMARY : GuiTheme.TEXT_DISABLED;
        GuiRenderer.drawText(context, name + ":", x + GuiTheme.PADDING, y + 2, textColor);
        
        // Input field
        int fieldY = y + 12;
        int fieldHeight = height - 14;
        int fieldColor = GuiTheme.interpolateColor(
            GuiTheme.TOGGLE_DISABLED,
            GuiTheme.SLIDER_TRACK,
            hoverAnimation.getValue()
        );
        GuiRenderer.drawRoundedRect(context, x + GuiTheme.PADDING, fieldY, width - GuiTheme.PADDING * 2, fieldHeight, 3, fieldColor);
        
        // Text content
        String displayText = value.isEmpty() && !focused ? placeholder : value;
        int displayTextColor = value.isEmpty() && !focused ? GuiTheme.TEXT_DISABLED : GuiTheme.TEXT_PRIMARY;
        
        // Clip text to fit in field
        int maxTextWidth = width - GuiTheme.PADDING * 3;
        String clippedText = clipText(context, displayText, maxTextWidth);
        
        GuiRenderer.drawText(context, clippedText, x + GuiTheme.PADDING * 2, fieldY + (fieldHeight - 8) / 2, displayTextColor);
        
        // Cursor
        if (focused && (System.currentTimeMillis() - lastCursorBlink) % 1000 < 500) {
            int cursorX = x + GuiTheme.PADDING * 2 + context.getTextRenderer().getWidth(value.substring(0, Math.min(cursorPosition, value.length())));
            context.fill(cursorX, fieldY + 2, cursorX + 1, fieldY + fieldHeight - 2, GuiTheme.TEXT_PRIMARY);
        }
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (!visible || !enabled) return false;
        
        boolean wasClicked = isMouseOver(mouseX, mouseY);
        setFocused(wasClicked);
        
        if (wasClicked && button == 0) {
            // Calculate cursor position from mouse click
            int fieldX = x + GuiTheme.PADDING * 2;
            int clickX = (int) mouseX - fieldX;
            
            cursorPosition = 0;
            for (int i = 0; i <= value.length(); i++) {
                int textWidth = i == 0 ? 0 : net.minecraft.client.MinecraftClient.getInstance().textRenderer.getWidth(value.substring(0, i));
                if (clickX <= textWidth + (i < value.length() ? net.minecraft.client.MinecraftClient.getInstance().textRenderer.getWidth(value.substring(i, i + 1)) / 2 : 0)) {
                    cursorPosition = i;
                    break;
                }
                cursorPosition = i + 1;
            }
            
            return true;
        }
        
        return false;
    }
    
    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (!focused || !enabled) return false;
        
        switch (keyCode) {
            case GLFW.GLFW_KEY_BACKSPACE:
                if (cursorPosition > 0) {
                    value = value.substring(0, cursorPosition - 1) + value.substring(cursorPosition);
                    cursorPosition--;
                    onTextChanged();
                }
                return true;
                
            case GLFW.GLFW_KEY_DELETE:
                if (cursorPosition < value.length()) {
                    value = value.substring(0, cursorPosition) + value.substring(cursorPosition + 1);
                    onTextChanged();
                }
                return true;
                
            case GLFW.GLFW_KEY_LEFT:
                if (cursorPosition > 0) {
                    cursorPosition--;
                }
                return true;
                
            case GLFW.GLFW_KEY_RIGHT:
                if (cursorPosition < value.length()) {
                    cursorPosition++;
                }
                return true;
                
            case GLFW.GLFW_KEY_HOME:
                cursorPosition = 0;
                return true;
                
            case GLFW.GLFW_KEY_END:
                cursorPosition = value.length();
                return true;
        }
        
        return false;
    }
    
    @Override
    public boolean charTyped(char chr, int modifiers) {
        if (!focused || !enabled) return false;
        
        if (numbersOnly && !Character.isDigit(chr) && chr != '.' && chr != '-') {
            return false;
        }
        
        if (chr >= 32 && chr != 127) { // Printable characters
            value = value.substring(0, cursorPosition) + chr + value.substring(cursorPosition);
            cursorPosition++;
            onTextChanged();
            return true;
        }
        
        return false;
    }
    
    private void onTextChanged() {
        if (onValueChange != null) {
            onValueChange.accept(value);
        }
    }
    
    private String clipText(DrawContext context, String text, int maxWidth) {
        if (context.getTextRenderer().getWidth(text) <= maxWidth) {
            return text;
        }
        
        for (int i = text.length() - 1; i >= 0; i--) {
            String clipped = text.substring(0, i) + "...";
            if (context.getTextRenderer().getWidth(clipped) <= maxWidth) {
                return clipped;
            }
        }
        
        return "...";
    }
    
    public void setFocused(boolean focused) {
        this.focused = focused;
        if (focused) {
            lastCursorBlink = System.currentTimeMillis();
        }
    }
    
    public void setValue(String value) {
        this.value = value != null ? value : "";
        this.cursorPosition = Math.min(this.cursorPosition, this.value.length());
    }
    
    public String getValue() {
        return value;
    }
    
    public void setPlaceholder(String placeholder) {
        this.placeholder = placeholder;
    }
    
    public void setOnValueChange(Consumer<String> onValueChange) {
        this.onValueChange = onValueChange;
    }
    
    public void setNumbersOnly(boolean numbersOnly) {
        this.numbersOnly = numbersOnly;
    }
}
