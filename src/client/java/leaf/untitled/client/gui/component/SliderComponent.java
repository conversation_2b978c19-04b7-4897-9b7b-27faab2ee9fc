package leaf.untitled.client.gui.component;

import leaf.untitled.client.gui.render.GuiRenderer;
import leaf.untitled.client.gui.theme.GuiTheme;
import net.minecraft.client.gui.DrawContext;

import java.util.function.Consumer;

public class SliderComponent extends GuiComponent {
    
    private double value;
    private double minValue;
    private double maxValue;
    private boolean dragging;
    private Consumer<Double> onValueChange;
    private String suffix = "";
    private int decimalPlaces = 1;
    
    public SliderComponent(String name, int x, int y, int width, double minValue, double maxValue, double defaultValue) {
        super(name, x, y, width, GuiTheme.COMPONENT_HEIGHT);
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.value = Math.max(minValue, Math.min(maxValue, defaultValue));
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        if (!visible) return;
        
        boolean isHovered = isMouseOver(mouseX, mouseY);
        hoverAnimation.setTarget(isHovered ? 1.0f : 0.0f);
        
        // Background
        int bgColor = GuiTheme.interpolateColor(
            GuiTheme.PANEL_BACKGROUND,
            GuiTheme.CATEGORY_BACKGROUND,
            hoverAnimation.getValue()
        );
        GuiRenderer.drawRoundedRect(context, x, y, width, height, GuiTheme.BORDER_RADIUS, bgColor);
        
        // Text
        int textColor = enabled ? GuiTheme.TEXT_PRIMARY : GuiTheme.TEXT_DISABLED;
        String displayText = name + ": " + formatValue(value) + suffix;
        GuiRenderer.drawText(context, displayText, x + GuiTheme.PADDING, y + 2, textColor);
        
        // Slider track
        int trackY = y + height - 8;
        int trackHeight = 4;
        int trackX = x + GuiTheme.PADDING;
        int trackWidth = width - GuiTheme.PADDING * 2;
        
        GuiRenderer.drawRoundedRect(context, trackX, trackY, trackWidth, trackHeight, trackHeight / 2, GuiTheme.SLIDER_TRACK);
        
        // Slider progress
        double progress = (value - minValue) / (maxValue - minValue);
        int progressWidth = (int) (trackWidth * progress);
        if (progressWidth > 0) {
            GuiRenderer.drawRoundedRect(context, trackX, trackY, progressWidth, trackHeight, trackHeight / 2, GuiTheme.ACCENT_PRIMARY);
        }
        
        // Slider thumb
        int thumbSize = 8;
        int thumbX = (int) (trackX + progressWidth - thumbSize / 2);
        int thumbY = trackY - (thumbSize - trackHeight) / 2;
        
        int thumbColor = dragging ? GuiTheme.ACCENT_HOVER : GuiTheme.SLIDER_THUMB;
        GuiRenderer.drawRoundedRect(context, thumbX, thumbY, thumbSize, thumbSize, thumbSize / 2, thumbColor);
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (!visible || !enabled || button != 0) return false;
        
        if (isMouseOver(mouseX, mouseY)) {
            dragging = true;
            updateValue(mouseX);
            return true;
        }
        
        return false;
    }
    
    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (dragging && button == 0) {
            dragging = false;
            return true;
        }
        return false;
    }
    
    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (dragging) {
            updateValue(mouseX);
            return true;
        }
        return false;
    }
    
    private void updateValue(double mouseX) {
        int trackX = x + GuiTheme.PADDING;
        int trackWidth = width - GuiTheme.PADDING * 2;
        
        double progress = Math.max(0, Math.min(1, (mouseX - trackX) / trackWidth));
        double newValue = minValue + (maxValue - minValue) * progress;
        
        setValue(newValue);
    }
    
    private String formatValue(double value) {
        if (decimalPlaces == 0) {
            return String.valueOf((int) value);
        } else {
            return String.format("%." + decimalPlaces + "f", value);
        }
    }
    
    public void setValue(double value) {
        this.value = Math.max(minValue, Math.min(maxValue, value));
        if (onValueChange != null) {
            onValueChange.accept(this.value);
        }
    }
    
    public double getValue() {
        return value;
    }
    
    public void setOnValueChange(Consumer<Double> onValueChange) {
        this.onValueChange = onValueChange;
    }
    
    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }
    
    public void setDecimalPlaces(int decimalPlaces) {
        this.decimalPlaces = decimalPlaces;
    }
}
