package leaf.untitled.client.gui.component;

import leaf.untitled.client.gui.animation.GuiAnimation;
import leaf.untitled.client.gui.render.GuiRenderer;
import leaf.untitled.client.gui.theme.GuiTheme;
import net.minecraft.client.gui.DrawContext;

public class ToggleComponent extends GuiComponent {
    
    private boolean value;
    private GuiAnimation toggleAnimation;
    private Runnable onToggle;
    
    public ToggleComponent(String name, int x, int y, int width, boolean defaultValue) {
        super(name, x, y, width, GuiTheme.COMPONENT_HEIGHT);
        this.value = defaultValue;
        this.toggleAnimation = new GuiAnimation(GuiTheme.ANIMATION_SPEED);
        this.toggleAnimation.setValue(defaultValue ? 1.0f : 0.0f);
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        if (!visible) return;
        
        boolean isHovered = isMouseOver(mouseX, mouseY);
        hoverAnimation.setTarget(isHovered ? 1.0f : 0.0f);
        
        // Background
        int bgColor = GuiTheme.interpolateColor(
            GuiTheme.PANEL_BACKGROUND,
            GuiTheme.CATEGORY_BACKGROUND,
            hoverAnimation.getValue()
        );
        GuiRenderer.drawRoundedRect(context, x, y, width, height, GuiTheme.BORDER_RADIUS, bgColor);
        
        // Text
        int textColor = enabled ? GuiTheme.TEXT_PRIMARY : GuiTheme.TEXT_DISABLED;
        GuiRenderer.drawText(context, name, x + GuiTheme.PADDING, y + (height - 8) / 2, textColor);
        
        // Toggle switch
        int switchWidth = 40;
        int switchHeight = 16;
        int switchX = x + width - switchWidth - GuiTheme.PADDING;
        int switchY = y + (height - switchHeight) / 2;
        
        // Switch background
        int switchBgColor = value ? GuiTheme.TOGGLE_ENABLED : GuiTheme.TOGGLE_DISABLED;
        GuiRenderer.drawRoundedRect(context, switchX, switchY, switchWidth, switchHeight, switchHeight / 2, switchBgColor);
        
        // Switch thumb
        int thumbSize = switchHeight - 4;
        int thumbX = (int) (switchX + 2 + (switchWidth - thumbSize - 4) * toggleAnimation.getValue());
        int thumbY = switchY + 2;
        GuiRenderer.drawRoundedRect(context, thumbX, thumbY, thumbSize, thumbSize, thumbSize / 2, GuiTheme.TEXT_PRIMARY);
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (!visible || !enabled || button != 0) return false;
        
        if (isMouseOver(mouseX, mouseY)) {
            toggle();
            return true;
        }
        
        return false;
    }
    
    @Override
    public void update() {
        super.update();
        toggleAnimation.update();
    }
    
    public void toggle() {
        setValue(!value);
        if (onToggle != null) {
            onToggle.run();
        }
    }
    
    public void setValue(boolean value) {
        this.value = value;
        toggleAnimation.setTarget(value ? 1.0f : 0.0f);
    }
    
    public boolean getValue() {
        return value;
    }
    
    public void setOnToggle(Runnable onToggle) {
        this.onToggle = onToggle;
    }
}
